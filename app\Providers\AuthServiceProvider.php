<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Laravel\Passport\Passport;
use App\Models\SaveJob;
use App\Policies\SaveJobPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        SaveJob::class => SaveJobPolicy::class,
    ];

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        
        // Trong Laravel 11, không sử dụng Passport::routes()
        // Thay vào đó, Laravel sẽ tự động đăng ký các routes cần thiết

        // Thiết lập thời gian hết hạn token
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::personalAccessTokensExpireIn(now()->addMonths(6));

        // Cấu hình các scope (phạm vi quyền hạn)
        Passport::tokensCan([
            'SUPPER-ADMIN-SETTING' => 'Thiết lập hệ thống',
            'ADMIN-SETTING'        => 'Cài đặt hệ thống',
            'VIEW-WORKFLOW'        => 'Xem quy trình',
            'CREATE-WORKFLOW'      => 'Tạo quy trình',
            'VIEW-JOB'             => 'Xem công việc',
            'CREATE-JOB'           => 'Tạo công việc',
            'VIEW-DASHBOARD'       => 'Xem báo cáo',
        ]);
    }
} 
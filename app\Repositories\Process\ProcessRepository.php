<?php
namespace App\Repositories\Process;

use App\Repositories\EloquentRepository;
use App\Enums\ProcessStatus;
use App\Repositories\User\UserRepositoryInterface;
use App\Services\JobPermissionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;

class ProcessRepository extends EloquentRepository implements ProcessRepositoryInterface
{
	private $userRepository;
	private $jobPermissionService;

	public function __construct(
		UserRepositoryInterface $userRepository, 
		JobPermissionService $jobPermissionService
	)
	{
		parent::__construct();
		$this->userRepository = $userRepository;
		$this->jobPermissionService = $jobPermissionService;
	}

	public function getModel()
	{
		return \App\Models\Process::class;
	}

	public function getWorkflowByScopeUses($search)
	{
		$select_columns = [
			'id',
			'name',
			'status',
		];

		$workflows = $this->model
			->select($select_columns)
			->with(['processVersionActive:id,process_id,form_id,scope_use,job_manager,followers'])
			->when(($search), function($query) use ($search) {
				$query->where('name', 'LIKE', '%' . $search . '%');
			})
			->where('status', ProcessStatus::ACTIVE->value)
			->limit(20)
			->get();

		// Xử lý where với cột scope_use để những user nào có quyền mới được sử dụng
		$userId = Auth::id(); // Lấy ID của user đang đăng nhập
        $workflows = $workflows->filter(function($workflow) use ($userId) {
            $scopeUses = $workflow->processVersionActive->scope_use;
            if (is_null($scopeUses)) {
                return true; // Nếu scope_use là null, mặc định được lấy
            }
            $usersInScope = $this->userRepository->getUserByOptionScopeRes($scopeUses, $workflow->id);
            return in_array($userId, array_column($usersInScope, 'value'));
        });
		// dd($workflows);
		return $workflows;	
	}

	public function getAllWorkflowRes($dataSearch)
	{
		// Validate input parameters
		$dataSearch = $dataSearch ?? [];

		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab'])
			? ProcessStatus::fromString($dataSearch['tab'])
			: ProcessStatus::ALL;

		$page = isset($dataSearch['page']) ? (int) $dataSearch['page'] : null;
        $perPage = isset($dataSearch['perPage']) ? (int) $dataSearch['perPage'] : null;
        $userId = Auth::id();

        // Validate pagination parameters
        if ($page !== null && $page < 1) {
            $page = 1;
        }
        if ($perPage !== null && ($perPage < 1 || $perPage > 100)) {
            $perPage = 10; // Default per page
        }

		$select_columns = [
			'id',
			'name',
			'description',
			'process_group_id',
			'status',
			'create_by',
			'created_at',
		];

		// Base query với relationships
		$baseQuery = $this->model
			->with([
				'processGroup:id,name',
				'createdBy:id,full_name',
				'processVersionActive:id,process_id,create_by,created_at,process_manager,followers',
				'processVersionActive.createdBy:id,full_name'
			])
			->select($select_columns);

        // Lấy tất cả workflows một lần để tối ưu performance
        $allWorkflows = $baseQuery->get();

        // Batch process permission checks và thêm can_process_manager flag
        $filteredAllWorkflows = $this->filterWorkflowsByPermissionWithManagerFlag($allWorkflows, $userId);

        // Tính counts cho từng status từ filtered workflows
        $statusList = ProcessStatus::cases();
        $counts = [];

        foreach ($statusList as $status) {
            if ($status === ProcessStatus::ALL) {
                $counts[strtolower($status->name)] = $filteredAllWorkflows->count();
            } else {
                $counts[strtolower($status->name)] = $filteredAllWorkflows
                    ->where('status', $status->value)
                    ->count();
            }
        }

        // Filter theo tab status
        if ($tabStatus !== ProcessStatus::ALL) {
            $filteredWorkflows = $filteredAllWorkflows->where('status', $tabStatus->value);
        } else {
            $filteredWorkflows = $filteredAllWorkflows;
        }

        // Apply sorting với xử lý relationship fields
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';

        $filteredWorkflows = $filteredWorkflows->sortBy(function($workflow) use ($orderBy) {
            // Handle relationship fields
            switch ($orderBy) {
                case 'name':
                    return $workflow->name ?? '';
                default:
                    return $workflow->{$orderBy} ?? '';
            }
        }, SORT_REGULAR, $orderDirection === 'desc');

        // Áp dụng pagination
        if ($page && $perPage) {
            $total = $filteredWorkflows->count();
            $offset = ($page - 1) * $perPage;
            $workflowsData = $filteredWorkflows->slice($offset, $perPage)->values();

            $workflows = new LengthAwarePaginator(
                $workflowsData,
                $total,
                $perPage,
                $page,
                [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]
            );
        } else {
            $workflows = [
                'data' => $filteredWorkflows->values(),
                'total' => $filteredWorkflows->count()
            ];
        }

        return [
            'workflows' => $workflows,
            'counts' => $counts
        ];
	}

	/**
	 * Filter workflows by user permission with optimized caching and add process manager flag
	 *
	 * @param \Illuminate\Support\Collection $workflows
	 * @param int $userId
	 * @return \Illuminate\Support\Collection
	 */
	private function filterWorkflowsByPermissionWithManagerFlag($workflows, $userId)
	{
		// Cache để tránh duplicate calls cho cùng một scope
		$scopeCache = [];

		return $workflows->filter(function($workflow) use ($userId, &$scopeCache) {
			$process_manager = $workflow->processVersionActive->process_manager ?? [];
			$followers = $workflow->processVersionActive->followers ?? [];

			// Check process_manager permission riêng
			$canProcessManager = false;
			if (!empty($process_manager)) {
				$managerCacheKey = md5(json_encode($process_manager) . '_manager_' . $workflow->id);

				if (isset($scopeCache[$managerCacheKey])) {
					$managerUserIds = $scopeCache[$managerCacheKey];
				} else {
					$usersInScopeManager = $this->userRepository->getUserByOptionScopeRes($process_manager, $workflow->id);
					$managerUserIds = array_column($usersInScopeManager, 'value');
					$scopeCache[$managerCacheKey] = $managerUserIds;
				}

				$canProcessManager = in_array($userId, $managerUserIds);
			}

			// Check followers permission
			$canFollower = false;
			if (!empty($followers)) {
				$followerCacheKey = md5(json_encode($followers) . '_follower_' . $workflow->id);

				if (isset($scopeCache[$followerCacheKey])) {
					$followerUserIds = $scopeCache[$followerCacheKey];
				} else {
					$usersInScopeFollower = $this->userRepository->getUserByOptionScopeRes($followers, $workflow->id);
					$followerUserIds = array_column($usersInScopeFollower, 'value');
					$scopeCache[$followerCacheKey] = $followerUserIds;
				}

				$canFollower = in_array($userId, $followerUserIds);
			}

			// Nếu không có quyền gì thì không cho access
			if (!$canProcessManager && !$canFollower) {
				return false;
			}

			// Thêm can_process_manager flag vào workflow object
			$workflow->can_process_manager = $canProcessManager;

			return true;
		});
	}

	public function showWorkflowDetailActive($workflowId)
	{
		// Định nghĩa các cột cần select để tối ưu query
		$select_column_workflow = [
			'id',
			'name',
			'description',
			'process_group_id',
			'status',
		];

		$select_column_process_version_active = [
			'id',
			'process_id',
			'form_id',
			'scope_use',
			'job_manager',
			'followers',
			'process_manager',
		];

		$select_column_form = [
			'id',
			'name',
			'description',
		];

		$select_column_field = [
			'id',
			'keyword',
			'display_name',
			'display_name_en',
			'type',
			'default_value',
			'required',
			'order',
			'min_equal',
			'max_equal',
			'stage_id',
			'placeholder',
			'placeholder_en',
			'column_width',
			'form_id',
			'not_edit',
			'multiple',
			'options',
			'object_table',
			'column_table',
			'sub_column_table',
			'parent_id',
			'is_active',
			'create_by',
		];

		$select_column_stage = [
			'id',
			'name',
		];

		// Thực hiện query một lần duy nhất với eager loading để tránh N+1 problem
		$workflow = $this->model
			->with([
				'processVersionActive' => function ($query) use ($select_column_process_version_active, $select_column_form, $select_column_field, $select_column_stage) {
					$query->select($select_column_process_version_active)
						->with([
							'form' => function ($query) use ($select_column_form) {
								$query->select($select_column_form);
							},
							'form.fields' => function ($query) use ($select_column_field) {
								$query->select($select_column_field)
									->with([
										'children' => function ($query) use ($select_column_field) {
											$query->select($select_column_field);
										}
									]);
							},
							'form.fields.stage' => function ($query) use ($select_column_stage) {
								$query->select($select_column_stage);
							},
						]);
				},
				'processVersionActive.stages',
				'processVersionActive.processTransitions',
				'processVersionActive.emailTemplates',
				'processVersionActive.syncGroups',
				'processVersionActive.actions',
				'processVersionActive.conditions',
			])
			->select($select_column_workflow)
			->find($workflowId);

		// Kiểm tra workflow có tồn tại không
		if (!$workflow) {
			return null;
		}

		// Kiểm tra processVersionActive có tồn tại không
		if (!$workflow->processVersionActive) {
			return $workflow; // Trả về workflow nhưng không có flow_transitions
		}

		// Lấy thông tin user và process version
		$user = Auth::user();
		$process_version_id = $workflow->processVersionActive->id;

		// Lấy flow transitions data
		$data_flow_transitions = $this->jobPermissionService->getFlowTransitionsForJob(null, $user, $process_version_id);

		// Gán flow_transitions vào workflow
		$workflow->flow_transitions = $data_flow_transitions['flow_transitions'];

		return $workflow;
	}
}
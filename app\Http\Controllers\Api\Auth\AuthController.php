<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Models\User;

class AuthController extends Controller
{
    /**
     * L<PERSON>y danh sách quyền của user
     * 
     * @param User $user Đối tượng người dùng
     * @return array Mảng các quyền của người dùng
     */
    private function getUserPermissions(User $user)
    {
        // Lấy danh sách quyền từ vai trò của người dùng
        $permissions = $user->getAllPermissions();
        if ($permissions->isEmpty()) {
            return [];
        }
        
        // Lấy danh sách slug các quyền
        return $permissions->pluck('slug')->toArray();
    }

    /**
     * Lấy phiên bản quyền của người dùng
     * 
     * @param User $user Đ<PERSON><PERSON> tượng người dùng
     * @return int Phiên bản quyền
     */
    private function getPermissionsVersion(User $user) {
        return $user->getPermissionsVersion();
    }
    
    /**
     * Lấy danh sách vai trò của user
     * 
     * @param User $user Đối tượng người dùng
     * @return array Mảng các vai trò của người dùng
     */
    private function getUserRoles(User $user)
    {
        return $user->roles->pluck('name')->toArray();
    }

    /**
     * Xử lý đăng nhập
     * 
     * @param Request $request Yêu cầu HTTP
     * @return \Illuminate\Http\JsonResponse Response JSON với token và thông tin người dùng
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'account_name' => 'required',
            'password' => 'required',
        ]);
        
        if (!Auth::attempt($credentials)) {
            return response()->json(['message' => 'Incorrect account or password'], 401);
        }

        $user = $request->user();
        $permissions = $this->getUserPermissions($user);
        $tokenResult = $user->createToken('Personal Access Token', $permissions);
        $token = $tokenResult->token;
        $token->save();

        $roles = $this->getUserRoles($user);
        $permissionsVersion = $this->getPermissionsVersion($user);

        // Set locale based on user's language preference
        $language = $user->language ?? config('app.locale');
        app()->setLocale($language);
        
        // Trả về token trong response JSON mà không set cookie ở backend
        return response()->json([
            'user' => $user->only(['id', 'full_name', 'email', 'language']),
            'permissions' => $permissions,
            'roles' => $roles,
            'permissions_version' => $permissionsVersion,
            'access_token' => $tokenResult->accessToken,
            'language' => $language,
            'cookie_data' => [
                'name' => 'access_token',
                'value' => $tokenResult->accessToken,
                'expires_days' => $token->expires_at ? max(0, Carbon::now()->diffInDays($token->expires_at, false)) : 1
            ]
        ]);
    }

    /**
     * Lấy thông tin user hiện tại và quyền MỚI NHẤT
     * 
     * @param Request $request Yêu cầu HTTP
     * @return \Illuminate\Http\JsonResponse Response JSON với thông tin người dùng và quyền
     */
    public function user(Request $request)
    {
        $user = $request->user();
        $permissions = $this->getUserPermissions($user);
        $roles = $this->getUserRoles($user);
        $permissionsVersion = $this->getPermissionsVersion($user);

        // Set locale based on user's language preference
        $language = $user->language ?? config('app.locale');
        app()->setLocale($language);
        
        return response()->json([
            'user' => $user->only(['id', 'full_name', 'email', 'language']),
            'permissions' => $permissions,
            'roles' => $roles,
            'permissions_version' => $permissionsVersion,
            'language' => $language,
        ]);
    }

    /**
     * Kiểm tra phiên bản quyền
     * 
     * @param Request $request Yêu cầu HTTP
     * @return \Illuminate\Http\JsonResponse Response JSON với phiên bản quyền
     */
    public function checkPermissionsVersion(Request $request)
    {
        return response()->json([
            'permissions_version' => $this->getPermissionsVersion($request->user()),
        ]);
    }

    /**
     * Xử lý đăng xuất
     * 
     * @param Request $request Yêu cầu HTTP
     * @return \Illuminate\Http\JsonResponse Response JSON với thông báo thành công
     */
    public function logout(Request $request)
    {
        $request->user()->token()->revoke();

        // Trả về thông báo thành công mà không xử lý cookie từ server
        return response()->json([
            'message' => 'Successfully logged out',
            'cookie_data' => [
                'name' => 'access_token',
                'should_delete' => true
            ]
        ]);
    }
    
    /**
     * Làm mới token
     * 
     * @param Request $request Yêu cầu HTTP
     * @return \Illuminate\Http\JsonResponse Response JSON với token mới và thông tin người dùng
     */
    public function refresh(Request $request)
    {
        // Hủy token cũ
        $user = $request->user();
        $request->user()->token()->revoke();
        
        // Tạo token mới
        $tokenResult = $user->createToken('Personal Access Token');
        $token = $tokenResult->token;
        $token->save();
        
        // Lấy thông tin quyền và vai trò
        $permissions = $this->getUserPermissions($user);
        $roles = $this->getUserRoles($user);
        $permissionsVersion = $this->getPermissionsVersion($user);
        
        // Trả về token mới để client tự xử lý
        return response()->json([
            'user' => $user->only(['id', 'full_name', 'email']),
            'permissions' => $permissions,
            'roles' => $roles,
            'permissions_version' => $permissionsVersion,
            'access_token' => $tokenResult->accessToken,
            'cookie_data' => [
                'name' => 'access_token',
                'value' => $tokenResult->accessToken,
                'expires_days' => $token->expires_at ? max(0, Carbon::now()->diffInDays($token->expires_at, false)) : 1
            ]
        ]);
    }
}

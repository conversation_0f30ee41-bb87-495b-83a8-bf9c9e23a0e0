<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Field\FieldController;
use App\Http\Controllers\Form\FormController;
use App\Http\Controllers\SelectOption\OptionController;
use App\Http\Controllers\Job\SaveJobController;
use App\Http\Controllers\Workflow\WorkflowController;
use App\Http\Controllers\ProcessGroup\ProcessGroupController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\ResetPasswordController;
use App\Http\Controllers\Api\Auth\TwoFactorController;
use App\Http\Controllers\Api\File\FileController;
use App\Http\Controllers\Api\Admin\PermissionController;
use App\Http\Controllers\Api\Admin\RoleController;
use App\Http\Controllers\Api\Admin\UserController;

Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('reset-password', [ResetPasswordController::class, 'sendMailResetPassword']);
    Route::post('new-password', [ResetPasswordController::class, 'updateNewPassword']);

    Route::middleware('auth:api')->group( function () {
        Route::get('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);
        Route::get('refresh', [AuthController::class, 'refresh']);
        Route::get('check-permissions', [AuthController::class, 'checkPermissionsVersion']);
    });
});


Route::group(['middleware' => ['auth:api']], function () {
    Route::middleware('scope:VIEW-WORKFLOW')->group(function () {
        Route::get('/tables', [FieldController::class, 'getTables']);
        Route::get('/columns', [FieldController::class, 'getColumns']);
        Route::get('/column-data', [FieldController::class, 'getColumnData']);
        Route::get('/option-users', [OptionController::class, 'getOptionUsers']);
        Route::get('/option-departments', [OptionController::class, 'getOptionDepartments']);
        Route::get('/option-job-position-systems', [OptionController::class, 'getJobPositionSystems']);
        Route::get('/option-rank-systems', [OptionController::class, 'getRankSystems']);
        Route::get('/option-workflows', [OptionController::class, 'getOptionWorkflows']);
        Route::get('/option-process-groups', [OptionController::class, 'getOptionProcessGroups']);
        Route::get('/option-scopes', [OptionController::class, 'getOptionScopes']);
        Route::get('/user-scopes', [OptionController::class, 'getUserByOptionScopes']);
        Route::post('/process-group', [ProcessGroupController::class, 'storeProcessGroup']);
        Route::get('/form-fields', [FormController::class, 'getFieldsByFormId']);
    });

    Route::prefix('workflows')->middleware('scope:VIEW-WORKFLOW')->group(function () {
        Route::get('/', [WorkflowController::class, 'getAllWorkflows']);
        Route::post('/', [WorkflowController::class, 'storeWorkflow'])->middleware('scope:CREATE-WORKFLOW');
        Route::get('/{workflowId}', [WorkflowController::class, 'showWorkflow']);
        Route::get('/{workflowId}/flow-transitions', [WorkflowController::class, 'getFlowTransitions']);
        Route::put('/{workflowId}/update-info', [WorkflowController::class, 'updateInfoWorkflow'])->middleware('scope:CREATE-WORKFLOW');
    });

    Route::prefix('jobs')->middleware('scope:VIEW-JOB')->group(function () {
        Route::post('/', [SaveJobController::class, 'storeJob'])->middleware('scope:CREATE-JOB');
        Route::get('/', [SaveJobController::class, 'index']);
        Route::get('/filters', [SaveJobController::class, 'getFilters']);
        Route::get('/{jobId}', [SaveJobController::class, 'show']);
        Route::put('/{jobId}', [SaveJobController::class, 'update'])->middleware('scope:CREATE-JOB');
        Route::delete('/{jobId}', [SaveJobController::class, 'destroy'])->middleware('scope:CREATE-JOB');
        Route::post('/execute', [SaveJobController::class, 'executeAction']);
        Route::put('/{jobId}/execute-back-to', [SaveJobController::class, 'executeActionBackTo']);
        Route::get('/{jobId}/field-values-stage', [SaveJobController::class, 'getJobFieldValuesByStage']);
    });

    Route::prefix('permissions')->middleware('scope:SUPPER-ADMIN-SETTING')->group(function () {
        Route::post('/', [PermissionController::class, 'storePermission']);
        Route::get('/', [PermissionController::class, 'getAllPermissions']);
        
        // Permission Groups routes
        Route::get('/groups', [PermissionController::class, 'getPermissionGroups']);
        Route::post('/groups', [PermissionController::class, 'storePermissionGroup']);
    });

    Route::prefix('roles')->middleware('scope:ADMIN-SETTING')->group(function () {
        Route::post('/', [RoleController::class, 'storeRole']);
        Route::get('/', [RoleController::class, 'getAllRoles']);
        Route::get('/options', [RoleController::class, 'getAllRoleOptions']);
    });

    Route::prefix('users')->middleware('scope:ADMIN-SETTING')->group(function () {
        Route::post('/', [UserController::class, 'storeUser']);
        Route::get('/', [UserController::class, 'getAllUsers']);
        Route::put('/{id}/roles', [UserController::class, 'updateUserRoles']);
    });
});

Route::get('files/{filename}', [FileController::class, 'show']);
Route::get('files/download/{filename}', [FileController::class, 'download']);


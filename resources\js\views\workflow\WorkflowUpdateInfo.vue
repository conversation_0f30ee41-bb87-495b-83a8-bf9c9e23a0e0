<template>
    <BModal 
        v-model="isVisible"
        size="lg" 
        hide-footer
        no-close-on-backdrop
        no-close-on-esc
        centered
        :title="$t('workflow.update_info')"
        @hidden="handleClose"
    >
        <Form ref="form" @submit="handleSubmit" :validation-schema="schema">
            <CRow class="mb-3">
                <CCol :xs="12">
                    <label class="mb-1">
                        {{ $t('workflow.name') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        v-model="state.dataWorkflow.name" 
                        name="workflow_name"
                        type="text" 
                        class="form-control" 
                        :placeholder="$t('workflow.name')"
                        maxlength="200" 
                    />
                    <ErrorMessage
                        as="div"
                        name="workflow_name"
                        class="text-danger"
                    />
                </CCol>
            </CRow>
            <CRow class="mb-3">
                <CCol :xs="12">
                    <div class="d-flex align-items-center">
                        <label class="mb-1">
                            {{ $t('workflow.process_group') }} 
                        </label>
                    </div>
                    <Multiselect
                        v-model="state.dataWorkflow.process_group" 
                        :placeholder="$t('workflow.choose')"
                        :close-on-select="false"
                        :filter-results="false"
                        :resolve-on-load="false"
                        :infinite="true"
                        :limit="10"
                        :clear-on-search="true"
                        :searchable="true"
                        :delay="0"
                        :min-chars="0"
                        :object="true"
                        :options="async (query) => {
                            return await debouncedGetOptionProcessGroups(query)
                        }"
                        @open="debouncedGetOptionProcessGroups('')"
                        :can-clear="false"
                    />
                </CCol>
            </CRow>
            <CRow class="mb-4">
                <CCol :xs="12">
                    <label class="mb-1">
                        {{ $t('workflow.description') }}
                    </label>
                    <Field 
                        v-model="state.dataWorkflow.description" 
                        name="description"
                        as="textarea" 
                        class="form-control" 
                        :placeholder="$t('workflow.description')"
                        rows="3"
                        maxlength="500" 
                    />
                </CCol>
            </CRow>
            <CRow>
                <CCol :xs="12" class="d-flex justify-content-end">
                    <CButton 
                        color="secondary"
                        class="me-2"
                        @click="handleClose"
                    >
                        {{ $t('common.close') }}
                    </CButton>
                    <CButton 
                        color="primary"
                        type="submit"
                        :disabled="setIsLoading"
                    >
                        <span v-if="setIsLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                        {{ $t('common.save') }}
                    </CButton>
                </CCol>
            </CRow>
        </Form>
    </BModal>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import { debounce } from 'lodash';
import Multiselect from '@vueform/multiselect';
import useWorkflow from '@/composables/workflow';
import useOptions from '@/composables/option';

export default defineComponent({
    name: 'WorkflowUpdateInfo',
    
    components: {
        Form,
        Field,
        ErrorMessage,
        Multiselect
    },
    
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        workflowData: {
            type: Object,
            default: () => ({})
        }
    },
    
    emits: ['close', 'updated'],
    
    setup(props, { emit }) {
        const { t } = useI18n();
        const { setIsLoading, updateInfoWorkflow } = useWorkflow();
        const { getProcessGroups } = useOptions();
        
        const state = reactive({
            dataWorkflow: {
                id: '',
                name: '',
                process_group: null,
                description: ''
            } as any,
        });
        
        const isVisible = computed({
            get: () => props.visible,
            set: (value) => {
                if (!value) {
                    emit('close');
                }
            }
        });
        
        const schema = yup.object().shape({
            workflow_name: yup.string().required(`${t('workflow.name')} ${t('workflow.validate.required')}`)
        });
        
        const getOptionProcessGroups = async (query: string) => {
			let result = await getProcessGroups(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: elem.name,
                    } 
                ));
            }
		}

		const debouncedGetOptionProcessGroups = debounce(getOptionProcessGroups, 500);
        
        watch(() => props.workflowData, (newVal) => {
            if (newVal) {
                state.dataWorkflow = {
                    id: newVal.id || '',
                    name: newVal.name || '',
                    process_group: newVal.process_group || null,
                    description: newVal.description || ''
                };
            }
        }, { immediate: true });
        
        const handleSubmit = async () => {
            const formData = {
                id: state.dataWorkflow.id,
                name: state.dataWorkflow.name,
                process_group_id: state.dataWorkflow.process_group?.value || null,
                description: state.dataWorkflow.description
            };
            
            const result = await updateInfoWorkflow(formData);
            
            if (result.status === 'success') {
                emit('updated');
                handleClose();
            }
        };
        
        const handleClose = () => {
            emit('close');
        };
        
        return {
            state,
            setIsLoading,
            isVisible,
            schema,
            debouncedGetOptionProcessGroups,
            handleSubmit,
            handleClose
        };
    }
});
</script>

<style scoped>
/* Add any custom styles here */
</style>
